# Atlas FastAPI Service - Environment Configuration Template
# Copy this file to .env and configure with your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Service Information
SERVICE_NAME=atlas-geospatial-api
VERSION=1.0.0
DEBUG=False
LOG_LEVEL=INFO

# CORS Settings (comma-separated list)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8000

# =============================================================================
# GOOGLE EARTH ENGINE AUTHENTICATION
# Choose ONE method below:
# =============================================================================

# METHOD 1: Local Development (JSON file)
# Place your service account JSON file in the keys/ directory
GOOGLE_APPLICATION_CREDENTIALS=./keys/gee-service-account.json
GOOGLE_CLOUD_PROJECT=your-gcp-project-id

# METHOD 2: Cloud Deployment (Individual Environment Variables) - RECOMMENDED
# Extract these values from your service account JSON file
# GOOGLE_CLOUD_PROJECT=your-gcp-project-id
# GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
# GOOGLE_PRIVATE_KEY_ID=your-private-key-id
# GOOGLE_CLIENT_ID=your-client-id
# GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour\nPrivate\nKey\nHere\n-----END PRIVATE KEY-----\n"

# =============================================================================
# PROCESSING CONSTRAINTS
# =============================================================================

# Maximum AOI area in square kilometers
MAX_AOI_AREA_KM2=100.0

# Request timeout in seconds  
REQUEST_TIMEOUT=300

# RAW DATA EXTRACTION - NO LIMITS
# Set to very high values to essentially disable all filtering
# This ensures users get raw, unprocessed data exactly as-is
GEE_MAX_FEATURES=*********

# Disable all processing thresholds (set to impossibly high values)
GEE_SIMPLIFICATION_THRESHOLD=*********
GEE_SAMPLING_THRESHOLD=*********

# =============================================================================
# OPENSTREETMAP CONFIGURATION
# =============================================================================

# Overpass API endpoint URL
OVERPASS_API_URL=https://overpass-api.de/api/interpreter

# Rate limiting for Overpass API (requests per second)
# Be conservative to respect community resources
OVERPASS_RATE_LIMIT=0.5