/* Documentation Pages Styles */

/* Layout */
.docs-layout {
    display: flex;
    height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* Sidebar */
.docs-sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.sidebar-header .logo i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    padding: var(--spacing-md) 0;
}

.nav-section {
    margin-bottom: var(--spacing-lg);
}

.nav-section h4 {
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-secondary);
}

.nav-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-section li {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    gap: var(--spacing-sm);
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
}

.nav-link.external i {
    font-size: 0.75rem;
}

/* Main Content */
.docs-main {
    flex: 1;
    overflow-y: auto;
    background: var(--bg-primary);
}

.docs-content {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-xl);
}

.hero-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.hero-content h1 i {
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.hero-stats,
.api-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Content Sections */
.content-section {
    margin-bottom: var(--spacing-xl);
}

.section-header {
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.section-header h2 i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.section-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
}

/* Problem Grid */
.problem-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.problem-item {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
    text-align: center;
}

.problem-item i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
}

.problem-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Solution Content */
.solution-intro {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.solution-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.feature {
    text-align: center;
    padding: var(--spacing-lg);
}

.feature i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Quick Start Steps */
.quick-start-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.step {
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.step-number {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.step-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Interface Features */
.interface-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
}

.feature-card i {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-card li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: var(--spacing-md);
}

.feature-card li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

/* Data Sources Grid */
.data-sources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.data-source {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
}

.source-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.source-header i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.source-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.source-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.source-stats .stat,
.source-stats .coverage {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.source-stats .coverage {
    background: var(--accent-color);
}

.data-source p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.source-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.feature-tag {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

/* Upload Info */
.upload-info {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
}

.supported-formats h3,
.upload-benefits h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.format-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.format-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-sm);
}

.format-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.format-item strong {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.format-item p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

/* Export Formats Grid */
.export-formats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.export-format {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
    text-align: center;
}

.export-format i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.export-format h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.export-format p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.format-features {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

/* Performance Content */
.performance-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.performance-table table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
    border-radius: var(--radius-card);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.performance-table th,
.performance-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.performance-table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: var(--text-primary);
}

.performance-table td {
    color: var(--text-secondary);
}

.performance-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.performance-tips li {
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.performance-tips strong {
    color: var(--text-primary);
}

/* Troubleshooting */
.troubleshooting-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.faq-item {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
}

.faq-item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.faq-item h3 i {
    color: var(--accent-color);
}

.faq-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Code Blocks */
.code-block {
    background: #1e1e1e;
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin: var(--spacing-md) 0;
    border: 1px solid var(--border-color);
}

.code-block pre {
    margin: 0;
    padding: var(--spacing-lg);
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block code {
    color: #f8f8f2;
    background: none;
    padding: 0;
    border-radius: 0;
}

/* API Documentation Specific */
.endpoint-info {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-card);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.method-url {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.method {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    color: white;
}

.method.get {
    background: #28a745;
}

.method.post {
    background: #007bff;
}

.method.put {
    background: #ffc107;
    color: #000;
}

.method.delete {
    background: #dc3545;
}

.url {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.params-table {
    margin: var(--spacing-lg) 0;
}

.params-table table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
    border-radius: var(--radius-card);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.params-table th,
.params-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.params-table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: var(--text-primary);
}

.params-table td {
    color: var(--text-secondary);
}

.params-table code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.8rem;
    color: var(--primary-color);
}

/* Info Box */
.info-box {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    margin: var(--spacing-md) 0;
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.info-box i {
    color: #3498db;
    margin-top: 2px;
}

.info-box p {
    margin: 0;
    color: var(--text-secondary);
}

/* Inline Links */
.inline-link {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: var(--transition-fast);
}

.inline-link:hover {
    border-bottom-color: var(--primary-color);
}

/* Footer */
.docs-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: var(--spacing-sm);
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .docs-layout {
        flex-direction: column;
    }
    
    .docs-sidebar {
        width: 100%;
        height: auto;
        order: 2;
    }
    
    .docs-main {
        order: 1;
    }
    
    .docs-content {
        padding: var(--spacing-lg);
    }
    
    .hero-content h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .hero-stats,
    .api-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .problem-grid,
    .solution-features,
    .interface-features,
    .data-sources-grid,
    .export-formats-grid {
        grid-template-columns: 1fr;
    }
    
    .performance-content {
        grid-template-columns: 1fr;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .method-url {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .sidebar-toggle {
        display: block;
    }
}

@media (max-width: 480px) {
    .docs-content {
        padding: var(--spacing-md);
    }
    
    .hero-content h1 {
        font-size: 1.8rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
}
