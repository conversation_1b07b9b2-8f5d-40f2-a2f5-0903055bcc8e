"""
Geometry Cleaner

Per-source building geometry cleaning for AI-derived datasets (Google Open Buildings,
Microsoft Building Footprints). OSM Buildings are left untouched by design.

Operations are strictly on shapes (geometry) only. Names/properties are preserved except
for optional additions like flags/diagnostics. Cleaning is opt-in and should be invoked
by API routes only when `clean=true`.
"""
from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any, Dict, Iterable, List, Optional, Tuple

import geopandas as gpd
import pandas as pd
from shapely.geometry import shape as shapely_shape, Polygon, mapping
from shapely.ops import unary_union
from shapely.strtree import STRtree

from src.api.schemas_v2 import DataSourceType as DataSourceTypeV2
from src.api.api_schemas import DataSourceType as DataSourceTypeV1

try:
    import pyproj  # noqa: F401
except Exception:  # pragma: no cover
    pyproj = None  # type: ignore

logger = logging.getLogger(__name__)


@dataclass
class GeometryCleanerConfig:
    overlap_threshold: float = 0.30         # 30% overlap of the smaller polygon
    edge_buffer_m: float = 0.5              # negative buffer to avoid edge-only overlaps
    strategy: str = "highest_confidence"    # or "largest_area" or "union"
    min_area_m2: float = 0.0                # drop very tiny polygons (< threshold)
    simplify_tolerance_m: Optional[float] = None  # topology-preserving simplify in meters
    make_valid: bool = True


class GeometryCleaner:
    """
    Clean building polygons for a single source independently.

    Supported source types (cleaned):
    - google_buildings
    - microsoft_buildings

    Ignored (returned as-is):
    - osm_buildings
    - non-building sources
    """

    BUILDING_SOURCES_V2 = {DataSourceTypeV2.GOOGLE_BUILDINGS, DataSourceTypeV2.MICROSOFT_BUILDINGS}
    BUILDING_SOURCES_V1 = {DataSourceTypeV1.GOOGLE_BUILDINGS, DataSourceTypeV1.MICROSOFT_BUILDINGS}

    def _is_building_source(self, source_type: Any) -> bool:
        try:
            return source_type in self.BUILDING_SOURCES_V2 or source_type in self.BUILDING_SOURCES_V1
        except Exception:
            return False

    def clean_source_buildings(
        self,
        source_type: Any,
        feature_collection_wgs84: Any,
        aoi_polygon_wgs84: Polygon,
        config: GeometryCleanerConfig,
    ) -> Any:
        """
        Clean a single source's building FeatureCollection and return a cleaned FeatureCollection.
        If the source is not in the AI building set, returns the input unchanged.
        """
        source_name = getattr(source_type, 'value', str(source_type))

        if not self._is_building_source(source_type):
            logger.info(f"Geometry cleaning skipped for {source_name} (not an AI building source)")
            return feature_collection_wgs84

        try:
            # Convert FeatureCollection -> GeoDataFrame (EPSG:4326)
            gdf_wgs84 = self._feature_collection_to_gdf(feature_collection_wgs84)
            original_count = len(gdf_wgs84)
            logger.info(f"Starting geometry cleaning for {source_name}: {original_count} features, config: min_area={config.min_area_m2}m², overlap_threshold={config.overlap_threshold}, strategy={config.strategy}")

            if gdf_wgs84.empty:
                logger.info(f"No features to clean for {source_name}")
                return feature_collection_wgs84

            # Determine metric CRS (UTM) from AOI centroid
            utm_epsg = self._suggest_utm_epsg(aoi_polygon_wgs84)
            if utm_epsg is None:
                # Fallback to Web Mercator for metrics
                utm_epsg = 3857
                logger.warning("Failed to determine UTM zone from AOI. Falling back to EPSG:3857 for cleaning metrics.")

            gdf_m = gdf_wgs84.set_crs(epsg=4326, allow_override=True).to_crs(epsg=utm_epsg)

            # Topology prep
            if config.make_valid:
                gdf_m["geometry"] = gdf_m.geometry.buffer(0)
                logger.debug(f"Applied make_valid buffer(0) to {source_name}")

            # Minimum area filtering
            if config.min_area_m2 and config.min_area_m2 > 0:
                before_area_filter = len(gdf_m)
                gdf_m = gdf_m[gdf_m.geometry.area >= float(config.min_area_m2)].copy()
                after_area_filter = len(gdf_m)
                removed_by_area = before_area_filter - after_area_filter
                if removed_by_area > 0:
                    logger.info(f"Removed {removed_by_area} features from {source_name} below minimum area threshold ({config.min_area_m2}m²)")

            if gdf_m.empty:
                logger.info(f"All features removed by area filtering for {source_name}")
                return feature_collection_wgs84

            # Deduplicate overlaps within this source
            before_dedup = len(gdf_m)
            cleaned_gdf_m = self._deduplicate_within_source(gdf_m, config)
            after_dedup = len(cleaned_gdf_m)
            removed_by_overlap = before_dedup - after_dedup
            if removed_by_overlap > 0:
                logger.info(f"Removed {removed_by_overlap} overlapping features from {source_name}")

            # Optional simplify
            if config.simplify_tolerance_m and config.simplify_tolerance_m > 0:
                cleaned_gdf_m["geometry"] = cleaned_gdf_m.geometry.simplify(config.simplify_tolerance_m, preserve_topology=True)
                logger.debug(f"Applied simplification ({config.simplify_tolerance_m}m tolerance) to {source_name}")

            # Reproject back to WGS84 and convert to FeatureCollection
            cleaned_wgs84 = cleaned_gdf_m.to_crs(epsg=4326)
            final_count = len(cleaned_wgs84)
            total_removed = original_count - final_count

            logger.info(f"Geometry cleaning completed for {source_name}: {original_count} → {final_count} features ({total_removed} removed)")
            return self._gdf_to_feature_collection(cleaned_wgs84, template_fc=feature_collection_wgs84)

        except Exception as e:
            logger.exception(f"Geometry cleaning failed for {source_name}: {e}")
            # Fail-safe: return original unmodified FC
            return feature_collection_wgs84

    # ---------------------------- internals ----------------------------

    def _feature_collection_to_gdf(self, feature_collection: Any) -> gpd.GeoDataFrame:
        """Convert geojson_pydantic FeatureCollection -> GeoDataFrame preserving properties."""
        try:
            features = getattr(feature_collection, "features", None)
            if features is None:
                return gpd.GeoDataFrame(columns=["geometry"])  # empty

            records: List[Dict[str, Any]] = []
            for f in features:
                props = dict(getattr(f, "properties", {}) or {})
                geom = getattr(f, "geometry", None)
                if geom is None:
                    continue
                shapely_geom = shapely_shape(geom.dict()) if hasattr(geom, "dict") else shapely_shape(geom)
                records.append({**props, "geometry": shapely_geom})

            gdf = gpd.GeoDataFrame(records, geometry="geometry", crs="EPSG:4326")

            # Normalize confidence for strategies (default 1.0 if missing)
            if "confidence" not in gdf.columns:
                gdf["confidence"] = 1.0

            return gdf
        except Exception as e:
            logger.exception(f"Failed to convert FeatureCollection to GeoDataFrame: {e}")
            return gpd.GeoDataFrame(columns=["geometry"])  # empty

    def _gdf_to_feature_collection(self, gdf: gpd.GeoDataFrame, template_fc: Any) -> Any:
        """Convert GeoDataFrame back to geojson_pydantic FeatureCollection, preserving properties."""
        from geojson_pydantic import Feature, FeatureCollection
        try:
            features: List[Feature] = []
            for _, row in gdf.iterrows():
                props = {k: v for k, v in row.items() if k != "geometry"}
                geom_mapping = mapping(row.geometry)
                features.append(Feature(type="Feature", properties=props, geometry=geom_mapping))
            return FeatureCollection(type="FeatureCollection", features=features)
        except Exception as e:
            logger.exception(f"Failed to convert GeoDataFrame to FeatureCollection: {e}")
            # Return the template to avoid breaking response shapes
            return template_fc

    def _suggest_utm_epsg(self, aoi_wgs84: Polygon) -> Optional[int]:
        try:
            if aoi_wgs84 is None or aoi_wgs84.is_empty:
                return None
            lon, lat = aoi_wgs84.centroid.x, aoi_wgs84.centroid.y
            zone = int((lon + 180) // 6) + 1
            if lat >= 0:
                epsg = 32600 + zone  # WGS84 UTM Northern Hemisphere
            else:
                epsg = 32700 + zone  # WGS84 UTM Southern Hemisphere
            # Basic validation of range
            if 32601 <= epsg <= 32660 or 32701 <= epsg <= 32760:
                return epsg
            return None
        except Exception:
            return None

    def _deduplicate_within_source(self, gdf_m: gpd.GeoDataFrame, cfg: GeometryCleanerConfig) -> gpd.GeoDataFrame:
        """
        Remove overlapping polygons within this source according to strategy.
        """
        if len(gdf_m) <= 1:
            return gdf_m

        geoms = list(gdf_m.geometry.values)
        tree = STRtree(geoms)
        index_by_geom = {id(geom): idx for idx, geom in enumerate(geoms)}

        # Track groups of overlapping indices
        visited: set[int] = set()
        groups: List[List[int]] = []

        for i, geom in enumerate(geoms):
            if i in visited:
                continue

            # Candidate indices by bbox intersect
            candidates = [index_by_geom.get(id(g)) for g in tree.query(geom)]
            candidates = [c for c in candidates if c is not None and c != i]

            current_group = [i]
            visited.add(i)

            for j in candidates:
                if j in visited:
                    continue
                if self._is_significant_overlap(geoms[i], geoms[j], cfg):
                    current_group.append(j)
                    visited.add(j)

            if len(current_group) > 1:
                groups.append(current_group)

        if not groups:
            logger.debug(f"No overlapping groups found in {len(gdf_m)} features")
            return gdf_m

        logger.debug(f"Found {len(groups)} overlapping groups with strategy '{cfg.strategy}'")

        to_drop: set[int] = set()
        union_rows: List[Dict[str, Any]] = []

        for group in groups:
            sub = gdf_m.iloc[group]
            if cfg.strategy == "largest_area":
                keep_idx = sub.geometry.area.idxmax()
                to_drop.update(set(group) - {keep_idx})
            elif cfg.strategy == "highest_confidence":
                if "confidence" in sub.columns:
                    keep_idx = sub["confidence"].astype(float).idxmax()
                else:
                    keep_idx = sub.geometry.area.idxmax()
                to_drop.update(set(group) - {keep_idx})
            elif cfg.strategy == "union":
                union_geom = unary_union(list(sub.geometry))
                # Use max confidence and carry representative properties (first row)
                rep = sub.iloc[0].drop(labels=["geometry"]).to_dict()
                if "confidence" in sub.columns:
                    try:
                        rep["confidence"] = float(sub["confidence"].astype(float).max())
                    except Exception:
                        pass
                union_rows.append({**rep, "geometry": union_geom, "source": "union"})
                to_drop.update(set(group))
            else:
                # Unknown strategy -> default to highest_confidence
                if "confidence" in sub.columns:
                    keep_idx = sub["confidence"].astype(float).idxmax()
                else:
                    keep_idx = sub.geometry.area.idxmax()
                to_drop.update(set(group) - {keep_idx})

        cleaned = gdf_m.drop(index=to_drop).copy()
        if union_rows:
            union_gdf = gpd.GeoDataFrame(union_rows, geometry="geometry", crs=gdf_m.crs)
            cleaned = gpd.GeoDataFrame(pd.concat([cleaned, union_gdf], ignore_index=True), geometry="geometry", crs=gdf_m.crs)

        return cleaned

    def _is_significant_overlap(self, g1, g2, cfg: GeometryCleanerConfig) -> bool:
        try:
            # Edge-only guard via negative buffer
            if cfg.edge_buffer_m and cfg.edge_buffer_m > 0:
                try:
                    b1 = g1.buffer(-cfg.edge_buffer_m)
                    b2 = g2.buffer(-cfg.edge_buffer_m)
                    if b1.is_empty or b2.is_empty or not b1.intersects(b2):
                        return False
                except Exception:
                    # If buffering fails, fall back to raw intersection
                    pass

            inter = g1.intersection(g2)
            if inter.is_empty:
                return False

            inter_area = getattr(inter, "area", 0.0) or 0.0
            if inter_area <= 0:
                return False

            a1 = getattr(g1, "area", 0.0) or 0.0
            a2 = getattr(g2, "area", 0.0) or 0.0
            smaller = min(a1, a2)
            if smaller <= 0:
                return False

            overlap_ratio = inter_area / smaller
            return overlap_ratio >= float(cfg.overlap_threshold)
        except Exception:
            return False
