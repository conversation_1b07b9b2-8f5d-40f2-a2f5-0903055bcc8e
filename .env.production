# Atlas FastAPI Service - Production Environment Template
# Configure this for production deployment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

SERVICE_NAME=atlas-geospatial-api
VERSION=1.0.0
DEBUG=False
LOG_LEVEL=INFO

# Production CORS settings - restrict to your domains
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# =============================================================================
# GOOGLE EARTH ENGINE CONFIGURATION
# =============================================================================

# Production service account credentials
GOOGLE_APPLICATION_CREDENTIALS=/app/keys/prod-gee-service-account.json
GOOGLE_CLOUD_PROJECT=your-production-project-id

# =============================================================================
# OPENSTREETMAP CONFIGURATION
# =============================================================================

OVERPASS_API_URL=https://overpass-api.de/api/interpreter
OVERPASS_TIMEOUT=25
OVERPASS_RATE_LIMIT=0.5

# =============================================================================
# PROCESSING CONSTRAINTS
# =============================================================================

MAX_AOI_AREA_KM2=100.0
MAX_FEATURES_PER_SOURCE=50000
MAX_RESPONSE_SIZE_MB=500.0
REQUEST_TIMEOUT=300

# =============================================================================
# REDIS CACHE (Recommended for production)
# =============================================================================

REDIS_URL=redis://redis-server:6379
CACHE_TTL=3600

# =============================================================================
# EXPORT CONFIGURATION
# =============================================================================

EXPORT_TEMP_DIR=/app/exports
EXPORT_CLEANUP_HOURS=24

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================

DEBUG=False
LOG_LEVEL=INFO