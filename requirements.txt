# Atlas FastAPI Service - Production Dependencies

# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Geospatial Core Libraries
geopandas==0.14.1
shapely==2.0.2
fiona==1.9.5
pyproj==3.6.1
rasterio==1.3.9
geojson-pydantic==1.1.0

# Data Source Libraries
earthengine-api==0.1.383
osmnx==1.7.1
overpy==0.7
requests==2.31.0

# Export Format Libraries
simplekml==1.3.6
ezdxf==1.1.4
openpyxl==3.1.2

# Async and Concurrency
aiohttp==3.9.1
aiofiles==23.2.1
asyncio-throttle==1.0.2

# Utilities
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
python-dotenv==1.0.0
click==8.1.7

# Optional: Caching and Performance
redis==5.0.1
hiredis==2.2.3

# Monitoring and Logging
structlog==23.2.0
prometheus-client==0.19.0

# Data Processing
pandas==2.1.4
numpy==1.25.2
matplotlib==3.8.2
folium==0.15.1

# Development and Testing (commented for production)
# pytest==7.4.3
# pytest-asyncio==0.21.1
# pytest-cov==4.1.0
# black==23.11.0
# isort==5.12.0
# flake8==6.1.0
# mypy==1.7.1
# pre-commit==3.6.0