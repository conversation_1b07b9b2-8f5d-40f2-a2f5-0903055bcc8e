# -*- coding: utf-8 -*-
"""81_buildings.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/81_buildings.ipynb

[![image](https://jupyterlite.rtfd.io/en/latest/_static/badge.svg)](https://demo.leafmap.org/lab/index.html?path=notebooks/81_buildings.ipynb)
[![image](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/opengeos/leafmap/blob/master/docs/notebooks/81_buildings.ipynb)
[![image](https://mybinder.org/badge_logo.svg)](https://mybinder.org/v2/gh/opengeos/leafmap/HEAD)

**Downloading Microsoft and Google Building Footprints**

This notebook demonstrates how to download Microsoft and Google Building Footprints and merge them into a single vector file.

- Microsoft Global Building Footprints: https://github.com/microsoft/GlobalMLBuildingFootprints
- Google Open Buildings: https://sites.research.google/open-buildings

Uncomment the following line to install [leafmap](https://leafmap.org) if needed.
"""

# %pip install -U leafmap geopandas

import leafmap

"""Specify the country name."""

country = "Libya"

"""Specify the number of files to download. Set to `None` to download all files."""

head = 2

"""Download the Microsoft building footprints."""

leafmap.download_ms_buildings(
    country, out_dir="buildings", merge_output=f"{country}_ms.shp", head=head
)

"""Display the Microsoft building footprints."""

m = leafmap.Map()
m.add_basemap("SATELLITE")
m.add_vector(f"{country}_ms.shp", layer_name="MS Buildings")
m

"""Download the Google building footprints."""

leafmap.download_google_buildings(
    country,
    out_dir="buildings",
    merge_output=f"{country}_google.shp",
    head=head,
    overwrite=True,
)

"""Display the Google building footprints."""

url = "https://sites.research.google/open-buildings/tiles.geojson"

m = leafmap.Map()
m.add_basemap("SATELLITE")
m.add_geojson(url, layer_name="Google Building Coverage")
m.add_vector(f"{country}_google.shp", layer_name="Google Buildings")
m