# -*- coding: utf-8 -*-
"""01_building_delineation.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/github/GFDRR/caribbean-rooftop-classification/blob/master/tutorials/01_building_delineation.ipynb
"""

# MIT License
#
#@title Copyright (c) 2023 The World Bank/GFDRR { display-mode: "form" }
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
# FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
# DEALINGS IN THE SOFTWARE.

"""# Building Footprint Delineation for Disaster Risk Reduction and Response (Part I)
Author: [Isabelle Tingzon](https://issa-tingzon.github.io/), [The World Bank/GFDRR](https://gfdrr.org/)

In this tutorial, you will learn how to automatically extract building footprints from aerial imagery (e.g. satellite images, drone images) by utilzing the [segment-geospatial](https://samgeo.gishub.org/) package  for segmenting geospatial data using the [Segment Anything Model (SAM)](https://segment-anything.com/).

This tutorial is Part 1 of the series on AI and Earth Observation for Disaster Risk Reduction and Response. In [Part 2: Rooftop Classification from Drone Imagery](https://colab.research.google.com/github/GFDRR/caribbean-rooftop-classification/blob/add/tutorials/tutorials/02_building_classification.ipynb), you will learn how to use the building footprint polygons generated in this tutorial for roof type and roof material classification.

# Introduction

Building footprints are essential for disaster risk management. By understanding the spatial distribution of buildings and their different characteristics, decision-makers can better plan for and respond to disaster events.  Building footprints are typically accessible via official cadastral databases; however, the high costs and long timeframes associated with collecting and maintaining vast amounts of data make it difficult for any developing countries to obtain up-to-date and granular building information.

In the face of data scarcity, humanitarian organizations and disaster response teams have turned to alternative data sources such as volunteered geographic information (VGI) and AI-generated building footprint datasets. Several initiatives for generating publicly available building footprints worldwide include:

- [OpenStreetMap (OSM)](https://www.openstreetmap.org/)
- [Microsoft Building Footprints](https://github.com/microsoft/GlobalMLBuildingFootprints)
- [Google Open Buildings](https://sites.research.google/open-buildings/#download)

But despite the increasing availability of building footprints, disaster risk management agencies still face challenges including:

- **The need for up-to-date building information.** Disaster response is time-sensitive and requires updated, oftentimes real-time information. Unfortunately, publicly available building footprints may not always be up-to-date at the time of the disaster event. To address this challenge, initiatives like [Mapathons organized by HOTOSM](https://www.hotosm.org/updates/mapathon-mapping-disaster-prone-areas/) help focus crowdsourcing efforts in disaster-prone/disaster-stricken areas. Other programs like the [Digital Earth Partnership](https://www.gfdrr.org/en/digitalearthpartnership) led by [GFDRR](https://gfdrr.org/) support local communities in collecting very high-resolution drone imagery which, in conjunction with AI, can be useful for rapidly generating up-to-date information in selected areas at very low costs.

- **Misalignment issues between building footprints and the underlying basemaps.** Due to differences in the basemaps used for building footprint digitization or extraction, publicly available building footprints may not completely align with the aerial images used by disaster response agencies. This misalignment may be an issue for downstream tasks, such as rooftop classification, which require the building footprints to align completely with the underlying aerial image.

<br>
<center><p><p> <img src="https://github.com/GFDRR/caribbean-rooftop-classification/blob/1d2b7923cb0d58e6bb569ef68bed0b9a96808a69/assets/building-footprints.png?raw=true" width="75%">
<br>
<small>Microsoft, Google Open Buildings, and OpenStreetMap building footprints overlayed on a <a href="https://map.openaerialmap.org/#/-61.43446447296735,15.447516421719763,17/square/032303002321/5a95e65b5a9ef7cb5d8e93fc?_k=kxgayk">drone image</a> of Salisbury, Dominica,  taken from  <a href="https://map.openaerialmap.org/">OpenAerialMap</a>.</small></center>

# Application Context
Under the **Digital Earth Project for Resilient Housing and Infrastructure in the Caribbean** at [GFDRR](https://gfdrr.org/) , we support capacity-building in the Caribbean by training local communities to leverage Earth observation (EO)-based solutions in support of resilient infrastructure and housing operations. This includes developing local skills and capabilities to produce and update critical building information needed for governments to bolster resilience in the region. Specifically, the program supports the training of local communities to operate UAVs or drones in order to enable more frequent and inexpensive collection of very high-resolution aerial images. From the drone images, AI models can then be used to extract meaningful information such as building footprints, roof material, and building damage levels.

This two-part tutorial provides a brief walkthrough of how to leverage existing AI tools for the automatic extraction of building footprints from drone images. This tutorial is intended for GIS practitioners, community mappers, disaster responders, and other non-technical stakeholders interested in the rapid extraction of meaningful information from VHR aerial images for disaster reduction and response.

# Segment Anything Model (SAM) for Geospatial Data

This tutorial leverages the [segment-geospatial](https://samgeo.gishub.org/) Python package for segmenting GeoTIFF files using the [Segment Anything Model (SAM)](https://segment-anything.com/) by Meta AI Research. You can read more about their work in their paper, [Segment Anything](https://arxiv.org/pdf/2304.02643.pdf).

This notebook also leverages the [Language Segment-Anything (LangSAM)](https://github.com/luca-medeiros/lang-segment-anything), which combines instance segmentation with text prompts to generate masks of specific object in remote sensing images. The examples in this notebook are based on the work of Lucas Osco on leveraging AI for Remote Sensing Applications.

For more information, details, and examples on how to use LangSAM and AI Remote Sensing, you can visit Luca Medeiros's GitHub page at https://github.com/luca-medeiros/lang-segment-anything and Lucas Osco's Github page https://github.com/LucasOsco/AI-RemoteSensing/, respectively.

### References
- Qiusheng Wu, & Lucas Osco. (2023). samgeo: A Python package for segmenting geospatial data with the Segment Anything Model (SAM). Zenodo. https://doi.org/10.5281/zenodo.7966658
- Lucas Osco. (2023). AI-RemoteSensing: a collection of Jupyter and Google Colaboratory notebooks dedicated to leveraging Artificial Intelligence (AI) in Remote Sensing applications.
- Lucas Prado Osco. (2023). AI-RemoteSensing. Github. https://github.com/LucasOsco/AI-RemoteSensing
-  Luca Medeiros. (2023). Github. https://github.com/luca-medeiros/lang-segment-anything
- Kirillov, A., Mintun, E., Ravi, N., Mao, H., Rolland, C., Gustafson, L., ... & Girshick, R. (2023). Segment anything. arXiv preprint arXiv:2304.02643.

# Enabling GPU in Google Colab
Before we start, you will need access to a GPU. Google Colab provides free access to computing resources including GPUs. The GPUs currently available in Colab include Nvidia T4s, P4s and P100s. The types of GPUs that are available in Colab vary over time. Premium GPUs can be accessed by purchasing a paid plan [See here for information](https://research.google.com/colaboratory/faq.html#gpu-availability).

To enable GPU in Google Colab:
1. Navigate to Edit→Notebook Settings or Runtime→Change Runtime Type.
2. Select GPU from the Hardware Accelerator drop-down.

# Install Dependencies
"""

import os
HOME = os.getcwd()

# Commented out IPython magic to ensure Python compatibility.
!git clone https://github.com/GFDRR/caribbean-rooftop-classification.git
!pip -q install -U segment-geospatial
!pip -q install leafmap huggingface_hub json_fix
# %cd caribbean-rooftop-classification

# Commented out IPython magic to ensure Python compatibility.
# Fixing error with GroundingDino on Colab: https://github.com/IDEA-Research/Grounded-Segment-Anything/issues/550
!git clone https://github.com/IDEA-Research/GroundingDINO.git
# %cd GroundingDINO/groundingdino/models/GroundingDINO/csrc/MsDeformAttn
!sed -i 's/value.type()/value.scalar_type()/g' ms_deform_attn_cuda.cu
!sed -i 's/value.scalar_type().is_cuda()/value.is_cuda()/g' ms_deform_attn_cuda.cu

# Commented out IPython magic to ensure Python compatibility.
# %cd {HOME}/caribbean-rooftop-classification/GroundingDINO
!pip -q install -e .
!pip -q install supervision

# Commented out IPython magic to ensure Python compatibility.
# %cd {HOME}/caribbean-rooftop-classification/

"""# Imports and Setup"""

# Commented out IPython magic to ensure Python compatibility.
import os
import gdown

from tqdm.notebook import tqdm
import matplotlib.pyplot as plt
import rasterio as rio
import geopandas as gpd
import leafmap
import torch

from rasterio.plot import show
from shapely.geometry import box
from samgeo import SamGeo

import sys
sys.path.insert(0, "./")
from utils import pred_utils

# Check is GPU is enabled
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print("Device: {}".format(device))
if str(device) == "cuda:0": # Get specific GPU model
  print("GPU: {}".format(torch.cuda.get_device_name(0)))

# %load_ext autoreload
# %autoreload 2

"""# Download Image

To download your aerial image to Google Colab, you can either:

1. Directly specify the URL of your TIFF image (`direct_download`).  Feel free to browse [OpenAerialMap](https://map.openaerialmap.org/) for examples of free and openly available aerial images (e.g drone images, satellite images).
2. Upload your aerial image to Google Drive and extract the Document ID from your file (`google_drive`). The Document ID can be found by right clicking on the file >> choose "Share" >> change access permissions to "Anyone with the link can view" >> choose "Copy link". The Document ID can be extract from the link as follows:

```
https://drive.google.com/file/d/<document_id>/view?usp=sharing
```

For example, if the link to your image file is `https://drive.google.com/file/d/1afodaBDzBZbCGMYyTLMcX3oYOjLLpPeI/view?usp=sharing`, the Document ID is `1afodaBDzBZbCGMYyTLMcX3oYOjLLpPeI`.


"""

image_file = "drone.tif"
#@markdown Select mode of download:
mode = 'google_drive'  #@param ["direct_download", "google_drive"]
#@markdown If you chose `direct_download`, specify the URL of the aerial image. Make sure that the file is publicly accessible.
url = "https://oin-hotosm.s3.amazonaws.com/5a95e6312553e6000ce5ad22/0/d3b89c33-ffca-4a2b-8629-ab4e849d968d.tif" #@param {type:"string"}
#@markdown If you chose `google_drive`, specify the Document ID of the file uploaded in Google Drive.
document_id = '1VylcfGaCwcYr5OLYWY_KAGz3sEpiIOZc'  #@param {type:"string"}

# Create output directory
out_dir = './output/'
if not os.path.exists(out_dir):
    os.makedirs(out_dir)
temp_dir = './output/temp/'

# The final building delineation output file
filename = "output.gpkg"
out_file = os.path.join(out_dir, filename)

# Create directory for the drone image
image_dir = "./data/rasters/drone/"
if not os.path.exists(image_dir):
    os.makedirs(image_dir)

if mode == "google_drive":
  url = f"https://drive.google.com/uc?id={document_id}"
  gdown.download(url, image_file, quiet=False)
if mode == "direct_download":
  !wget {url} -O {image_file}

"""# Visualize Image
Note: For large images (> 100 MB), you may opt to **skip this step**. Loading large image files can take up a lot of RAM; if you're using the free tier of Google Colab, this may cause your session to crash.
"""

fig, ax = plt.subplots(figsize=(8,8))
image = rio.open(image_file)
show(image, ax=ax);

"""# LangSAM for Geospatial Data

For model prediction, you'll need to set the appropriate parameters.
- `text_prompt` (default: "house") - the text prompt from which to generate the object mask. You can experiment with different values (e.g. "building", "infrastructure", etc.)

- `box_threshold` (default: 0.30) - this threshold value is used for object detection in the image and ranges between 0 to 1. A higher value makes the model more selective, identifying only the most confident object instances, leading to fewer overall detections. A lower value, conversely, makes the model more tolerant, leading to increased detections, including potentially less confident ones. ([Source](https://github.com/LucasOsco/AI-RemoteSensing/blob/main/image_notebook/sam_text_v01.ipynb))

- `text_threshold` (default: 0.30) - this threshold value is used to associate the detected objects with the provided text prompt and ranges between 0 to 1. A higher value requires a stronger association between the object and the text prompt, leading to more precise but potentially fewer associations. A lower value allows for looser associations, which could increase the number of associations but also introduce less precise matches. ([Source](https://github.com/LucasOsco/AI-RemoteSensing/blob/main/image_notebook/sam_text_v01.ipynb))

- `max_area` (default: 1000) - the maximum building size in square meters. Any polygon with area > max_area will be removed or filtered out.

- `min_area` (default: 5) - the minimum building size in square meters. Any polygon with area < min_area will be removed or filtered out.

- `tile_size` (default: 3000) - the size of the sliding window in pixels. Predicting on very large images (> 500 MB) may cause your colab session to crash. In order to process very large images, we generate overlapping sliding windows across the image and merge the predictions across all windows.

- `tolerance` (default: 0.000005) : the tolerance parameter as specified in the GeoPandas simplify function, which returns a a simplified representation of each geometry. The algorithm (Douglas-Peucker) recursively splits the original line into smaller parts and connects these parts’ endpoints by a straight line. Then, it removes all points whose distance to the straight line is smaller than tolerance. It does not move any points and it always preserves endpoints of the original line or polygon. ([Source](https://geopandas.org/en/stable/docs/reference/api/geopandas.GeoSeries.simplify.html))
"""

polygons = pred_utils.segment_image(
    image_file=image_file,
    out_dir=temp_dir,
    out_file=out_file,
    text_prompt="house",
    box_threshold=0.3,
    text_threshold=0.3,
    max_area=1000,
    min_area=5,
    tile_size=1000,
    tolerance=0.000005
);

"""# Visualize Predictions
Note: Again, for large images, you may opt to skip this step to avoid the session from crashing.
"""

color = "#3476e0"
fig, ax = plt.subplots(figsize=(8,8))
polygons.plot(facecolor=color, edgecolor=color,  linewidth=3, alpha=0.5, ax=ax, aspect=1)
show(image, ax=ax);

"""# Interactive Editing

Here, we use [leafmap](https://leafmap.org/) to interactively visualize and edit the model predictions. While leafmap is useful for editing and deleting existing polygons. For more complex operations (e.g. splitting, copying, or moving polygons), you may want to use [QGIS](https://qgis.org/en/site/) (open source) or [ArcGIS](https://www.arcgis.com/index.html) (commercial software).
"""

import locale
def getpreferredencoding(do_setlocale = True):
    return "UTF-8"
locale.getpreferredencoding = getpreferredencoding
!pip install -q localtileserver

"""If you encounter an error from running the cell below, just rerun the previous cell (installing `localtileserver`)."""

data = gpd.GeoDataFrame({"id":1,"geometry":[box(*image.bounds)]}, crs=image.crs).to_crs("EPSG:4326")
centroid = data.geometry.values[0].centroid

map = leafmap.Map(center=[centroid.y, centroid.x], zoom=20)
map.add_raster(image_file, layer_name="Image", zoom=20)
style = {
    "color": "#3388ff",
    "weight": 2,
    "fillColor": "#7c4185",
    "fillOpacity": 0.5,
}
map.edit_vector(out_file, layer_name="Vector", style=style)
map

"""# Save the vector file and upload to Google Drive"""

#@markdown Specify your output filename
filename = "output_edited.gpkg" #@param {type:"string"}
out_file = os.path.join(out_dir, filename)
polygons.to_file(out_file, driver='GPKG')

from google.colab import auth
from googleapiclient.http import MediaFileUpload
from googleapiclient.discovery import build

auth.authenticate_user()
drive_service = build('drive', 'v3')
file_metadata = {'name': out_file.split("/")[-1]}
media = MediaFileUpload(
    out_file,
    resumable=True
)
created = drive_service.files().create(
    body=file_metadata,
    media_body=media,
    fields='id'
).execute()
print('Upload to Google Drive done.')
print(f'Please download {file_metadata["name"]} manually from Google Drive.')
print(f'Search link: https://drive.google.com/drive/search?q={file_metadata["name"]}')