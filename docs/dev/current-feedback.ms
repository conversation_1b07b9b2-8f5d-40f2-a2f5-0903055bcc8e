now tell me about the frontend, we have some progress feedback where by for instance when a user uploads a design we tell them processing design and when a user tries to run analysis we tell them running analysis and when they try to download we tell them downloading features thankyou, i need you to assess this carefully and then give me a recommendation on how i can make this cleaner, i was thinking we can include it on the toast notification service so they will show up on toast notifications and for instance fior this particular ones we can implament a progress bar or somethingm, this will help users get better visual feedback while the map is still visible (right now the map turns white and has a centered rectangle, while this works, visually it does not look so great)