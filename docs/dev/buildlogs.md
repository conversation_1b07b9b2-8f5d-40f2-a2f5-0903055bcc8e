===== Application Startup at 2025-09-09 18:47:25 =====

└─ platform: huggingface_spaces
├─ platform: huggingface_spaces
├─ config: loaded (api, gee, osm, web, export)
└─ config: loaded
INFO:     Started server process [1]
INFO:     Waiting for application startup.
[2025-09-09 18:48:23] atlas-gis-api v1.0.0 starting...
└─ static: mounted (/web/static)
DEBUG:google_auth_httplib2:Making request: POST https://oauth2.googleapis.com/token
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): oauth2.googleapis.com:443
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): earthengine.googleapis.com:443
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /v1/projects/grand-airport-464420-e3/algorithms?prettyPrint=false&alt=json HTTP/1.1" 200 None
└─ gee-auth: credentials ok
✓ Using individual environment variables for authentication
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
└─ gee-conn: test passed
└─ sources: ready (7: microsoft, google, osm-buildings, osm-roads, osm-railways, osm-landmarks, osm-natural)
└─ server: ready on :7860

[startup completed in 0.9s]
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:7860 (Press CTRL+C to quit)
INFO:     10.16.41.192:6070 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.41.192:6070 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.19.200:60137 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:     10.16.19.200:60137 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.41.192:6070 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.41.192:6070 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.19.200:26402 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.41.192:37778 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.41.192:52047 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.41.192:52047 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.38.14:15733 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:     10.16.19.200:2490 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     10.16.46.162:27592 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.46.162:27592 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.46.162:27592 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.46.162:61245 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:     10.16.38.14:9485 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.38.14:9485 - "GET /web/ HTTP/1.1" 200 OK
INFO:src.api.routes.design_upload:===== NEW REQUEST: POST /api/v2/designs/upload at 2025-09-09 18:57:42 =====
INFO:src.api.routes.design_upload:Processing design upload: MOFARM FEEDER.kmz (application/octet-stream)
INFO:src.api.routes.design_upload:Read 9187 bytes from MOFARM FEEDER.kmz
INFO:src.core.parsers.design_parser:Processing kmz file: MOFARM FEEDER.kmz
INFO:src.core.parsers.design_parser:Successfully parsed MOFARM FEEDER.kmz: 11 layers, 201 features in 0.05s
INFO:src.core.storage.design_storage:Stored design 84d17baf-a0fe-414f-9878-e3d047c28ed4 with 11 layers
INFO:src.api.routes.design_upload:Design upload completed: 84d17baf-a0fe-414f-9878-e3d047c28ed4 in 0.07s, 201 features
INFO:     10.16.38.14:11874 - "POST /api/v2/designs/upload HTTP/1.1" 200 OK
INFO:src.api.routes.design_upload:===== NEW REQUEST: POST /api/v2/designs/render at 2025-09-09 18:57:42 =====
INFO:src.api.routes.design_upload:Rendering design 84d17baf-a0fe-414f-9878-e3d047c28ed4
INFO:src.api.routes.design_upload:Design rendering completed: 84d17baf-a0fe-414f-9878-e3d047c28ed4 in 0.19s, 201 features
INFO:     10.16.41.192:9056 - "POST /api/v2/designs/render HTTP/1.1" 200 OK
INFO:     10.16.19.200:31166 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.19.200:31166 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.46.162:22221 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.41.192:33435 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:     10.16.41.192:33435 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.46.162:22221 - "GET /web/ HTTP/1.1" 200 OK
INFO:src.api.routes.extract_v2:===== NEW REQUEST: POST /api/v2/extract at 2025-09-09 19:00:46 =====
INFO:src.api.routes.extract_v2:Starting raw data extraction job 508c8a8e-47d8-4218-a462-3c17c618db63
INFO:src.core.services.processing_service:Processing extraction for 3 data sources
INFO:src.core.services.processing_service:AOI validated: 0.16 km²
DEBUG:urllib3.connectionpool:Resetting dropped connection: oauth2.googleapis.com
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Resetting dropped connection: earthengine.googleapis.com
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Google Earth Engine initialized successfully
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Found 655 Google Open Buildings in AOI
INFO:src.core.data_sources.google_earth_engine.google_buildings:Extracting ALL 655 Google Open Buildings - no limits applied
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Successfully extracted and processed 655 Google Open Buildings with clean properties
INFO:src.core.services.processing_service:Source google_buildings completed: 655 features in 8.49s
INFO:src.core.data_sources.openstreetmap.osm_roads:Converting 117 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_roads:Successfully converted 117 OSM roads features
INFO:src.core.services.processing_service:Source osm_roads completed: 117 features in 0.56s
INFO:src.core.data_sources.openstreetmap.osm_landmarks:Converting 24 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_landmarks:Successfully converted 24 OSM landmarks features
INFO:src.core.services.processing_service:Source osm_landmarks completed: 24 features in 3.10s
INFO:src.core.services.processing_service:Completed processing: 3 sources processed
INFO:src.api.routes.extract_v2:Job 508c8a8e-47d8-4218-a462-3c17c618db63 completed in 11.70s: 796 features from 3/3 sources
INFO:     10.16.14.52:3478 - "POST /api/v2/extract HTTP/1.1" 200 OK
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/download/508c8a8e-47d8-4218-a462-3c17c618db63/kmz at 2025-09-09 19:01:37 =====
INFO:src.api.routes.download_v2:Download request: job=508c8a8e-47d8-4218-a462-3c17c618db63, format=kmz
INFO:     10.16.41.192:13564 - "GET /api/v2/download/508c8a8e-47d8-4218-a462-3c17c618db63/kmz HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: POST /api/v2/download-inline/kmz at 2025-09-09 19:01:44 =====
INFO:     10.16.46.162:43248 - "POST /api/v2/download-inline/kmz HTTP/1.1" 200 OK
INFO:     10.16.41.192:41239 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.19.200:1933 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.41.192:41239 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/download/508c8a8e-47d8-4218-a462-3c17c618db63/kmz at 2025-09-09 19:05:00 =====
INFO:src.api.routes.download_v2:Download request: job=508c8a8e-47d8-4218-a462-3c17c618db63, format=kmz
INFO:     10.16.14.52:21657 - "GET /api/v2/download/508c8a8e-47d8-4218-a462-3c17c618db63/kmz HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: POST /api/v2/download-inline/kmz at 2025-09-09 19:05:02 =====
INFO:     10.16.38.14:2385 - "POST /api/v2/download-inline/kmz HTTP/1.1" 200 OK
INFO:src.api.routes.design_upload:===== NEW REQUEST: POST /api/v2/designs/upload at 2025-09-09 19:16:06 =====
INFO:src.api.routes.design_upload:Processing design upload: atlas_results_508c8a8e-47d8-4218-a462-3c17c618db63 (1).kmz (application/octet-stream)
INFO:src.api.routes.design_upload:Read 110911 bytes from atlas_results_508c8a8e-47d8-4218-a462-3c17c618db63 (1).kmz
INFO:src.core.parsers.design_parser:Processing kmz file: atlas_results_508c8a8e-47d8-4218-a462-3c17c618db63 (1).kmz
INFO:src.core.parsers.design_parser:Successfully parsed atlas_results_508c8a8e-47d8-4218-a462-3c17c618db63 (1).kmz: 3 layers, 796 features in 0.18s
INFO:src.core.storage.design_storage:Stored design 29329731-4e4d-4bb6-aaa4-2f99c67a60a7 with 3 layers
INFO:src.api.routes.design_upload:Design upload completed: 29329731-4e4d-4bb6-aaa4-2f99c67a60a7 in 0.97s, 796 features
INFO:     10.16.19.200:15629 - "POST /api/v2/designs/upload HTTP/1.1" 200 OK
INFO:src.api.routes.design_upload:===== NEW REQUEST: POST /api/v2/designs/render at 2025-09-09 19:16:07 =====
INFO:src.api.routes.design_upload:Rendering design 29329731-4e4d-4bb6-aaa4-2f99c67a60a7
INFO:src.api.routes.design_upload:Design rendering completed: 29329731-4e4d-4bb6-aaa4-2f99c67a60a7 in 0.02s, 796 features
INFO:     10.16.46.162:38471 - "POST /api/v2/designs/render HTTP/1.1" 200 OK
INFO:     10.16.41.192:10613 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.46.162:63803 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.41.192:10613 - "GET /css/atlas.css HTTP/1.1" 200 OK
INFO:     10.16.19.200:15764 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:     10.16.19.200:15764 - "GET / HTTP/1.1" 307 Temporary Redirect
INFO:     10.16.19.200:15764 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.46.162:17215 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.46.162:54028 - "GET /css/atlas.css HTTP/1.1" 304 Not Modified
INFO:     10.16.38.14:9121 - "GET /js/atlas.js HTTP/1.1" 304 Not Modified
INFO:     10.16.38.14:28598 - "GET /js/atlas.js HTTP/1.1" 200 OK
INFO:src.api.routes.extract_v2:===== NEW REQUEST: POST /api/v2/extract at 2025-09-09 19:25:14 =====
INFO:src.api.routes.extract_v2:Starting raw data extraction job 1cd834c2-6b45-4187-a43d-33d24865e87d
INFO:src.core.services.processing_service:Processing extraction for 6 data sources
INFO:src.core.services.processing_service:AOI validated: 0.28 km²
DEBUG:urllib3.connectionpool:Resetting dropped connection: oauth2.googleapis.com
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Resetting dropped connection: earthengine.googleapis.com
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Google Earth Engine initialized successfully
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Found 1499 Google Open Buildings in AOI
INFO:src.core.data_sources.google_earth_engine.google_buildings:Extracting ALL 1499 Google Open Buildings - no limits applied
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Successfully extracted and processed 1499 Google Open Buildings with clean properties
INFO:src.core.services.processing_service:Source google_buildings completed: 1499 features in 5.42s
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Google Earth Engine initialized successfully
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Found 595 Microsoft buildings in AOI
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Extracting ALL 595 Microsoft buildings - no limits applied
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Successfully extracted and processed 595 Microsoft buildings with clean properties
INFO:src.core.services.processing_service:Source microsoft_buildings completed: 595 features in 3.80s
ERROR:src.core.data_sources.openstreetmap.osm_landmarks:Failed to extract osm_landmarks: Data source 'osm_landmarks' error: Overpass query failed: 504, message='Gateway Timeout', url=URL('https://overpass-api.de/api/interpreter')
ERROR:src.core.services.processing_service:Source osm_landmarks failed after 5.99s: Data source 'osm_landmarks' error: Data source 'osm_landmarks' error: Overpass query failed: 504, message='Gateway Timeout', url=URL('https://overpass-api.de/api/interpreter')
INFO:src.core.data_sources.openstreetmap.osm_natural:Converting 3 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_natural:Successfully converted 3 OSM natural features
INFO:src.core.services.processing_service:Source osm_natural completed: 3 features in 6.28s
INFO:src.core.data_sources.openstreetmap.osm_buildings:Converting 1908 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_buildings:Successfully converted 1907 OSM buildings features
INFO:src.core.services.processing_service:Source osm_buildings completed: 1907 features in 7.00s
ERROR:src.core.data_sources.openstreetmap.osm_roads:Failed to extract osm_roads: Data source 'osm_roads' error: Overpass query failed: 504, message='Gateway Timeout', url=URL('https://overpass-api.de/api/interpreter')
ERROR:src.core.services.processing_service:Source osm_roads failed after 7.11s: Data source 'osm_roads' error: Data source 'osm_roads' error: Overpass query failed: 504, message='Gateway Timeout', url=URL('https://overpass-api.de/api/interpreter')
INFO:src.core.services.processing_service:Completed processing: 6 sources processed
INFO:src.api.routes.extract_v2:Job 1cd834c2-6b45-4187-a43d-33d24865e87d completed in 16.48s: 4004 features from 4/6 sources
INFO:     10.16.38.14:57644 - "POST /api/v2/extract HTTP/1.1" 200 OK
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:10 =====
INFO:     10.16.19.200:64732 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:16 =====
INFO:     10.16.41.192:4852 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:19 =====
INFO:     10.16.19.200:45004 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:24 =====
INFO:     10.16.38.14:2081 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:26 =====
INFO:     10.16.38.14:2081 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:29 =====
INFO:     10.16.19.200:30194 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:33 =====
INFO:     10.16.46.162:39584 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.46.162:39584 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:36 =====
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:40 =====
INFO:     10.16.19.200:25461 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:43 =====
INFO:     10.16.19.200:35161 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:49 =====
INFO:     10.16.46.162:19743 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:52 =====
INFO:     10.16.41.192:52197 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:26:55 =====
INFO:     10.16.19.200:65484 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:00 =====
INFO:     10.16.19.200:1603 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:11 =====
INFO:     10.16.19.200:49923 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:14 =====
INFO:     10.16.19.200:49923 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:16 =====
INFO:     10.16.19.200:49923 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:19 =====
INFO:     10.16.19.200:49923 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:24 =====
INFO:     10.16.46.162:25486 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:28 =====
INFO:     10.16.46.162:7012 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.41.192:3501 - "GET /css/atlas.css HTTP/1.1" 304 Not Modified
INFO:     10.16.46.162:12828 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:34 =====
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:36 =====
INFO:     10.16.19.200:22137 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:39 =====
INFO:     10.16.46.162:53197 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.38.14:9080 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:41 =====
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:44 =====
INFO:     10.16.19.200:9041 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:49 =====
INFO:     10.16.41.192:61386 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:54 =====
INFO:     10.16.41.192:3661 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:27:58 =====
INFO:     10.16.46.162:1719 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:00 =====
INFO:     10.16.19.200:25848 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:03 =====
INFO:     10.16.41.192:5527 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:06 =====
INFO:     10.16.19.200:29121 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.46.162:13375 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:09 =====
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:13 =====
INFO:     10.16.46.162:58363 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:18 =====
INFO:     10.16.19.200:24398 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:22 =====
INFO:     10.16.46.162:42558 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:25 =====
INFO:     10.16.41.192:60396 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:29 =====
INFO:     10.16.46.162:54607 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.46.162:4647 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:33 =====
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:35 =====
INFO:     10.16.38.14:47230 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:src.api.routes.download_v2:===== NEW REQUEST: GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status at 2025-09-09 19:28:39 =====
INFO:     10.16.46.162:5781 - "GET /api/v2/job/1cd834c2-6b45-4187-a43d-33d24865e87d/status?include_results=true HTTP/1.1" 404 Not Found
INFO:     10.16.46.162:5781 - "GET /web/ HTTP/1.1" 200 OK
INFO:     10.16.41.192:35870 - "GET /js/atlas.js HTTP/1.1" 304 Not Modified
INFO:     10.16.46.162:42013 - "GET /js/atlas.js HTTP/1.1" 304 Not Modified
INFO:src.api.routes.extract_v2:===== NEW REQUEST: POST /api/v2/extract at 2025-09-09 19:34:17 =====
INFO:src.api.routes.extract_v2:Starting raw data extraction job 1ed44c66-7b8c-4b09-8689-0e19c21bfa28
INFO:src.core.services.processing_service:Processing extraction for 5 data sources
INFO:src.core.services.processing_service:AOI validated: 0.18 km²
DEBUG:urllib3.connectionpool:Resetting dropped connection: oauth2.googleapis.com
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:Resetting dropped connection: earthengine.googleapis.com
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Google Earth Engine initialized successfully
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Found 960 Google Open Buildings in AOI
INFO:src.core.data_sources.google_earth_engine.google_buildings:Extracting ALL 960 Google Open Buildings - no limits applied
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.google_buildings:Successfully extracted and processed 960 Google Open Buildings with clean properties
INFO:src.core.services.processing_service:Source google_buildings completed: 960 features in 4.96s
DEBUG:urllib3.connectionpool:https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "GET /$discovery/rest?version=v1&prettyPrint=false HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Google Earth Engine initialized successfully
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Found 379 Microsoft buildings in AOI
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Extracting ALL 379 Microsoft buildings - no limits applied
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 503 None
WARNING:googleapiclient.http:Sleeping 0.60 seconds before retry 1 of 5 for request: POST https://earthengine.googleapis.com/v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json, after 503
DEBUG:urllib3.connectionpool:https://earthengine.googleapis.com:443 "POST /v1/projects/grand-airport-464420-e3/value:compute?prettyPrint=false&alt=json HTTP/1.1" 200 None
INFO:src.core.data_sources.google_earth_engine.microsoft_buildings:Successfully extracted and processed 379 Microsoft buildings with clean properties
INFO:src.core.services.processing_service:Source microsoft_buildings completed: 379 features in 13.22s
INFO:src.core.data_sources.openstreetmap.osm_roads:Converting 52 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_roads:Successfully converted 52 OSM roads features
INFO:src.core.services.processing_service:Source osm_roads completed: 52 features in 0.55s
INFO:src.core.data_sources.openstreetmap.osm_landmarks:Converting 1 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_landmarks:Successfully converted 1 OSM landmarks features
INFO:src.core.services.processing_service:Source osm_landmarks completed: 1 features in 0.78s
INFO:src.core.data_sources.openstreetmap.osm_natural:Converting 0 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_natural:Successfully converted 0 OSM natural features
INFO:src.core.services.processing_service:Source osm_natural completed: 0 features in 6.47s
INFO:src.core.services.processing_service:Completed processing: 5 sources processed
INFO:src.api.routes.extract_v2:Job 1ed44c66-7b8c-4b09-8689-0e19c21bfa28 completed in 24.83s: 1392 features from 5/5 sources
INFO:     10.16.41.192:5971 - "POST /api/v2/extract HTTP/1.1" 200 OK
INFO:     10.16.46.162:46926 - "GET /css/atlas.css HTTP/1.1" 304 Not Modified
INFO:src.api.routes.extract_v2:===== NEW REQUEST: POST /api/v2/extract at 2025-09-09 19:36:55 =====
INFO:src.api.routes.extract_v2:Starting raw data extraction job 9bcb20d5-5326-42c0-bc7a-6177803b168f
INFO:src.core.services.processing_service:Processing extraction for 1 data sources
INFO:src.core.services.processing_service:AOI validated: 0.18 km²
INFO:src.core.data_sources.openstreetmap.osm_buildings:Converting 1472 raw OSM elements to GeoJSON
INFO:src.core.data_sources.openstreetmap.osm_buildings:Successfully converted 1472 OSM buildings features
INFO:src.core.services.processing_service:Source osm_buildings completed: 1472 features in 1.05s
INFO:src.core.services.processing_service:Completed processing: 1 sources processed
INFO:src.api.routes.extract_v2:Job 9bcb20d5-5326-42c0-bc7a-6177803b168f completed in 1.06s: 1472 features from 1/1 sources
INFO:     10.16.19.200:37690 - "POST /api/v2/extract HTTP/1.1" 200 OK